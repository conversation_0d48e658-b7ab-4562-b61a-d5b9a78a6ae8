using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing.Aggregates;
using TenantDB.Core.EventSourcing.Events;

namespace TenantDB.Tests.Integration;

public class TransactionIntegrationTests
{
    private readonly Mock<IDataLayer> _mockDataLayer;
    private readonly Mock<ILogger<DapperDataLayer>> _mockDataLayerLogger;

    public TransactionIntegrationTests()
    {
        _mockDataLayer = new Mock<IDataLayer>();
        _mockDataLayerLogger = new Mock<ILogger<DapperDataLayer>>();
    }

    [Fact]
    public async Task TransactionWorkflow_CreatePersonAndUpdate_ShouldWorkCorrectly()
    {
        // This is more of a conceptual test since we're using mocks
        // In a real integration test, you'd use a test database
        
        // Arrange
        var mockSession = new Mock<IDatabaseSession>();
        var mockEventStore = new Mock<TenantDB.Core.EventSourcing.IEventStore>();
        var mockAggregateRepo = new Mock<TenantDB.Core.EventSourcing.IAggregateRepository>();
        
        _mockDataLayer.Setup(x => x.CreateSessionAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSession.Object);
        
        mockSession.Setup(x => x.EventStore).Returns(mockEventStore.Object);
        mockSession.Setup(x => x.AggregateRepository).Returns(mockAggregateRepo.Object);
        mockSession.Setup(x => x.BeginTransaction()).Returns(Mock.Of<System.Data.IDbTransaction>());
        mockSession.Setup(x => x.HasActiveTransaction).Returns(true);
        
        mockEventStore.Setup(x => x.SaveEventAsync(It.IsAny<TenantDB.Core.EventSourcing.Models.BaseEvent>()))
            .ReturnsAsync(true);
        
        mockAggregateRepo.Setup(x => x.SaveAsync(It.IsAny<TenantDB.Core.EventSourcing.Models.Aggregate>()))
            .ReturnsAsync(true);

        // Simulate event source creation
        var eventSource = new TenantDB.Core.EventSourcing.Models.EventSource
        {
            SourceId = Guid.NewGuid(),
            SourceName = "test_api",
            SourceType = TenantDB.Core.EventSourcing.Models.SourceType.Api
        };
        
        mockEventStore.Setup(x => x.GetEventSourceByNameAsync("test_api"))
            .ReturnsAsync(eventSource);

        // Act
        using var session = await _mockDataLayer.Object.CreateSessionAsync("test_database");
        session.BeginTransaction();
        
        // Create person
        var personId = Guid.NewGuid();
        var createEvent = PersonAggregate.CreatePerson(personId, "John", "Doe", "<EMAIL>");
        createEvent.SourceId = eventSource.SourceId;
        
        var saveEventResult = await session.EventStore.SaveEventAsync(createEvent);
        
        var person = new PersonAggregate { Id = personId };
        person.Apply(createEvent);
        var saveAggregateResult = await session.AggregateRepository.SaveAsync(person);
        
        // Update person
        var updateData = new TenantDB.Core.EventSourcing.Events.PersonUpdatedEventData
        {
            FirstName = "John",
            LastName = "Smith",
            Email = "<EMAIL>"
        };
        var updateEvent = new TenantDB.Core.EventSourcing.Events.PersonUpdatedEvent(personId, updateData);
        updateEvent.SourceId = eventSource.SourceId;
        updateEvent.Version = 2;
        
        var saveUpdateEventResult = await session.EventStore.SaveEventAsync(updateEvent);
        person.Apply(updateEvent);
        var saveUpdatedAggregateResult = await session.AggregateRepository.SaveAsync(person);
        
        await session.CommitAsync();

        // Assert
        saveEventResult.Should().BeTrue();
        saveAggregateResult.Should().BeTrue();
        saveUpdateEventResult.Should().BeTrue();
        saveUpdatedAggregateResult.Should().BeTrue();
        
        // Verify the session methods were called
        mockSession.Verify(x => x.BeginTransaction(), Times.Once);
        mockSession.Verify(x => x.CommitAsync(), Times.Once);
        mockEventStore.Verify(x => x.SaveEventAsync(It.IsAny<TenantDB.Core.EventSourcing.Models.BaseEvent>()), Times.Exactly(2));
        mockAggregateRepo.Verify(x => x.SaveAsync(It.IsAny<TenantDB.Core.EventSourcing.Models.Aggregate>()), Times.Exactly(2));
    }

    [Fact]
    public async Task TransactionWorkflow_WithRollback_ShouldNotPersistChanges()
    {
        // Arrange
        var mockSession = new Mock<IDatabaseSession>();
        var mockEventStore = new Mock<TenantDB.Core.EventSourcing.IEventStore>();
        var mockAggregateRepo = new Mock<TenantDB.Core.EventSourcing.IAggregateRepository>();
        
        _mockDataLayer.Setup(x => x.CreateSessionAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSession.Object);
        
        mockSession.Setup(x => x.EventStore).Returns(mockEventStore.Object);
        mockSession.Setup(x => x.AggregateRepository).Returns(mockAggregateRepo.Object);
        mockSession.Setup(x => x.BeginTransaction()).Returns(Mock.Of<System.Data.IDbTransaction>());
        mockSession.Setup(x => x.HasActiveTransaction).Returns(true);
        
        // Simulate failure on second operation
        mockEventStore.SetupSequence(x => x.SaveEventAsync(It.IsAny<TenantDB.Core.EventSourcing.Models.BaseEvent>()))
            .ReturnsAsync(true)  // First save succeeds
            .ReturnsAsync(false); // Second save fails
        
        mockAggregateRepo.Setup(x => x.SaveAsync(It.IsAny<TenantDB.Core.EventSourcing.Models.Aggregate>()))
            .ReturnsAsync(true);

        var eventSource = new TenantDB.Core.EventSourcing.Models.EventSource
        {
            SourceId = Guid.NewGuid(),
            SourceName = "test_api",
            SourceType = TenantDB.Core.EventSourcing.Models.SourceType.Api
        };

        // Act
        using var session = await _mockDataLayer.Object.CreateSessionAsync("test_database");
        session.BeginTransaction();
        
        // Create person (succeeds)
        var personId = Guid.NewGuid();
        var createEvent = PersonAggregate.CreatePerson(personId, "John", "Doe", "<EMAIL>");
        createEvent.SourceId = eventSource.SourceId;
        
        var saveEventResult1 = await session.EventStore.SaveEventAsync(createEvent);
        
        // Update person (fails)
        var updateData = new TenantDB.Core.EventSourcing.Events.PersonUpdatedEventData
        {
            FirstName = "John",
            LastName = "Smith",
            Email = "<EMAIL>"
        };
        var updateEvent = new TenantDB.Core.EventSourcing.Events.PersonUpdatedEvent(personId, updateData);
        updateEvent.SourceId = eventSource.SourceId;
        updateEvent.Version = 2;
        
        var saveEventResult2 = await session.EventStore.SaveEventAsync(updateEvent);
        
        // Since second operation failed, rollback
        if (!saveEventResult2)
        {
            await session.RollbackAsync();
        }

        // Assert
        saveEventResult1.Should().BeTrue();
        saveEventResult2.Should().BeFalse();
        
        // Verify rollback was called
        mockSession.Verify(x => x.BeginTransaction(), Times.Once);
        mockSession.Verify(x => x.RollbackAsync(), Times.Once);
        mockSession.Verify(x => x.CommitAsync(), Times.Never);
    }

    [Fact] 
    public async Task DatabaseSession_CreateSessionAndAccessServices_ShouldProvideCorrectInstances()
    {
        // Arrange
        var mockSession = new Mock<IDatabaseSession>();
        var mockEventStore = new Mock<TenantDB.Core.EventSourcing.IEventStore>();
        var mockAggregateRepo = new Mock<TenantDB.Core.EventSourcing.IAggregateRepository>();
        
        _mockDataLayer.Setup(x => x.CreateSessionAsync("test_db"))
            .ReturnsAsync(mockSession.Object);
        
        mockSession.Setup(x => x.EventStore).Returns(mockEventStore.Object);
        mockSession.Setup(x => x.AggregateRepository).Returns(mockAggregateRepo.Object);

        // Act
        using var session = await _mockDataLayer.Object.CreateSessionAsync("test_db");

        // Assert
        session.Should().NotBeNull();
        session.EventStore.Should().Be(mockEventStore.Object);
        session.AggregateRepository.Should().Be(mockAggregateRepo.Object);
        
        _mockDataLayer.Verify(x => x.CreateSessionAsync("test_db"), Times.Once);
    }
}