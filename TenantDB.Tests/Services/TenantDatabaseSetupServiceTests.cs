using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using TenantDB.Core.Configuration;
using TenantDB.Core.DataLayer;
using TenantDB.Core.Services;

namespace TenantDB.Tests.Services;

public class TenantDatabaseSetupServiceTests
{
    private readonly Mock<IDataLayer> _mockDataLayer;
    private readonly Mock<ILogger<TenantDatabaseSetupService>> _mockLogger;
    private readonly DatabaseConfiguration _configuration;
    private readonly TenantDatabaseSetupService _service;

    public TenantDatabaseSetupServiceTests()
    {
        _mockDataLayer = new Mock<IDataLayer>();
        _mockLogger = new Mock<ILogger<TenantDatabaseSetupService>>();
        _configuration = new DatabaseConfiguration
        {
            Host = "localhost",
            Port = 5432,
            Database = "testdb",
            Username = "testuser",
            Password = "testpass"
        };

        _service = new TenantDatabaseSetupService(
            _mockDataLayer.Object,
            _mockLogger.Object,
            _configuration,
            "test-sql-path");
    }

    [Fact]
    public void GetDatabaseName_ShouldReturnConfiguredDatabaseName()
    {
        // Act
        var databaseName = _service.GetDatabaseName();

        // Assert
        databaseName.Should().Be("testdb");
    }

    [Fact]
    public async Task DatabaseExistsAsync_WhenDatabaseExists_ShouldReturnTrue()
    {
        // Arrange
        _mockDataLayer
            .Setup(x => x.DatabaseExistsAsync("testdb"))
            .ReturnsAsync(true);

        // Act
        var result = await _service.DatabaseExistsAsync();

        // Assert
        result.Should().BeTrue();
        _mockDataLayer.Verify(
            x => x.DatabaseExistsAsync("testdb"),
            Times.Once);
    }

    [Fact]
    public async Task DatabaseExistsAsync_WhenDatabaseDoesNotExist_ShouldReturnFalse()
    {
        // Arrange
        _mockDataLayer
            .Setup(x => x.DatabaseExistsAsync("testdb"))
            .ReturnsAsync(false);

        // Act
        var result = await _service.DatabaseExistsAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task DatabaseExistsAsync_WhenExceptionOccurs_ShouldReturnFalse()
    {
        // Arrange
        _mockDataLayer
            .Setup(x => x.DatabaseExistsAsync("testdb"))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _service.DatabaseExistsAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task RemoveDatabaseAsync_WhenSuccessful_ShouldReturnTrue()
    {
        // Arrange
        _mockDataLayer
            .Setup(x => x.DropDatabaseAsync("testdb"))
            .ReturnsAsync(true);

        // Act
        var result = await _service.RemoveDatabaseAsync();

        // Assert
        result.Should().BeTrue();
        _mockDataLayer.Verify(
            x => x.DropDatabaseAsync("testdb"),
            Times.Once);
    }

    [Fact]
    public async Task RemoveDatabaseAsync_WhenFailed_ShouldReturnFalse()
    {
        // Arrange
        _mockDataLayer
            .Setup(x => x.DropDatabaseAsync("testdb"))
            .ReturnsAsync(false);

        // Act
        var result = await _service.RemoveDatabaseAsync();

        // Assert
        result.Should().BeFalse();
    }
}
