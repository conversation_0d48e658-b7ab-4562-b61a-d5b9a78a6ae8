using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Data;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing;

namespace TenantDB.Tests.DataLayer;

public class DatabaseSessionTests
{
    private readonly Mock<IDbConnection> _mockConnection;
    private readonly Mock<IDbTransaction> _mockTransaction;
    private readonly Mock<ILogger<DatabaseSession>> _mockLogger;
    private readonly Mock<IEventStore> _mockEventStore;
    private readonly Mock<IAggregateRepository> _mockAggregateRepository;

    public DatabaseSessionTests()
    {
        _mockConnection = new Mock<IDbConnection>();
        _mockTransaction = new Mock<IDbTransaction>();
        _mockLogger = new Mock<ILogger<DatabaseSession>>();
        _mockEventStore = new Mock<IEventStore>();
        _mockAggregateRepository = new Mock<IAggregateRepository>();
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Arrange
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        // Act
        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Assert
        session.Connection.Should().Be(_mockConnection.Object);
        session.HasActiveTransaction.Should().BeFalse();
        session.Transaction.Should().BeNull();
    }

    [Fact]
    public void BeginTransaction_WhenNoActiveTransaction_ShouldCreateTransaction()
    {
        // Arrange
        _mockConnection.Setup(x => x.BeginTransaction()).Returns(_mockTransaction.Object);
        
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Act
        var transaction = session.BeginTransaction();

        // Assert
        transaction.Should().Be(_mockTransaction.Object);
        session.HasActiveTransaction.Should().BeTrue();
        session.Transaction.Should().Be(_mockTransaction.Object);
    }

    [Fact]
    public void BeginTransaction_WhenActiveTransactionExists_ShouldThrowInvalidOperationException()
    {
        // Arrange
        _mockConnection.Setup(x => x.BeginTransaction()).Returns(_mockTransaction.Object);
        
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);
        session.BeginTransaction();

        // Act & Assert
        session.Invoking(s => s.BeginTransaction())
            .Should().Throw<InvalidOperationException>()
            .WithMessage("A transaction is already active. Commit or rollback the current transaction before beginning a new one.");
    }

    [Fact]
    public async Task CommitAsync_WithActiveTransaction_ShouldCommitAndDisposeTransaction()
    {
        // Arrange
        _mockConnection.Setup(x => x.BeginTransaction()).Returns(_mockTransaction.Object);
        
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);
        session.BeginTransaction();

        // Act
        await session.CommitAsync();

        // Assert
        _mockTransaction.Verify(x => x.Commit(), Times.Once);
        _mockTransaction.Verify(x => x.Dispose(), Times.Once);
        session.HasActiveTransaction.Should().BeFalse();
        session.Transaction.Should().BeNull();
    }

    [Fact]
    public async Task CommitAsync_WithNoActiveTransaction_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Act & Assert
        await session.Invoking(s => s.CommitAsync())
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("No active transaction to commit.");
    }

    [Fact]
    public async Task RollbackAsync_WithActiveTransaction_ShouldRollbackAndDisposeTransaction()
    {
        // Arrange
        _mockConnection.Setup(x => x.BeginTransaction()).Returns(_mockTransaction.Object);
        
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);
        session.BeginTransaction();

        // Act
        await session.RollbackAsync();

        // Assert
        _mockTransaction.Verify(x => x.Rollback(), Times.Once);
        _mockTransaction.Verify(x => x.Dispose(), Times.Once);
        session.HasActiveTransaction.Should().BeFalse();
        session.Transaction.Should().BeNull();
    }

    [Fact]
    public async Task RollbackAsync_WithNoActiveTransaction_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Act & Assert
        await session.Invoking(s => s.RollbackAsync())
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("No active transaction to rollback.");
    }

    [Fact]
    public void EventStore_ShouldReturnEventStoreFromFactory()
    {
        // Arrange
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Act
        var eventStore = session.EventStore;

        // Assert
        eventStore.Should().Be(_mockEventStore.Object);
    }

    [Fact]
    public void AggregateRepository_ShouldReturnAggregateRepositoryFromFactory()
    {
        // Arrange
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        using var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Act
        var aggregateRepository = session.AggregateRepository;

        // Assert
        aggregateRepository.Should().Be(_mockAggregateRepository.Object);
    }

    [Fact]
    public void Dispose_WithActiveTransaction_ShouldRollbackAndDispose()
    {
        // Arrange
        _mockConnection.Setup(x => x.BeginTransaction()).Returns(_mockTransaction.Object);
        
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);
        session.BeginTransaction();

        // Act
        session.Dispose();

        // Assert
        _mockTransaction.Verify(x => x.Rollback(), Times.Once);
        _mockTransaction.Verify(x => x.Dispose(), Times.Once);
        _mockConnection.Verify(x => x.Dispose(), Times.Once);
    }

    [Fact]
    public void Dispose_WithNoActiveTransaction_ShouldDisposeConnectionOnly()
    {
        // Arrange
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory = (conn, trans) => _mockEventStore.Object;
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory = (conn, trans) => _mockAggregateRepository.Object;

        var session = new DatabaseSession(_mockConnection.Object, _mockLogger.Object, eventStoreFactory, aggregateRepositoryFactory);

        // Act
        session.Dispose();

        // Assert
        _mockTransaction.Verify(x => x.Rollback(), Times.Never);
        _mockTransaction.Verify(x => x.Dispose(), Times.Never);
        _mockConnection.Verify(x => x.Dispose(), Times.Once);
    }
}