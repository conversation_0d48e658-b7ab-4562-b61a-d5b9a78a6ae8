using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Configuration;
using TenantDB.Core.Connection;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing;
using TenantDB.Core.Services;

namespace TenantDB.Core.Extensions;

/// <summary>
/// Extension methods for registering TenantDB services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers all TenantDB services with the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">Database configuration</param>
    /// <param name="sqlScriptsPath">Path to SQL scripts directory (default: "sql")</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddTenantDB(
        this IServiceCollection services,
        DatabaseConfiguration configuration,
        string sqlScriptsPath = "sql")
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));
        
        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));

        // Register configuration
        services.AddSingleton(configuration);

        // Register core services
        services.AddScoped<IConnectionFactory, PostgreSqlConnectionFactory>();
        services.AddScoped<IDataLayer, DapperDataLayer>();

        // Register event sourcing services
        services.AddScoped<IEventStore, DapperEventStore>();
        services.AddScoped<IAggregateRepository, DapperAggregateRepository>();

        // Register tenant database setup service with SQL scripts path
        services.AddScoped<ITenantDatabaseSetupService>(provider =>
            new TenantDatabaseSetupService(
                provider.GetRequiredService<IDataLayer>(),
                provider.GetRequiredService<ILogger<TenantDatabaseSetupService>>(),
                provider.GetRequiredService<DatabaseConfiguration>(),
                sqlScriptsPath));

        return services;
    }

    /// <summary>
    /// Registers all TenantDB services with the dependency injection container using configuration from appsettings
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configurationSection">Configuration section containing database settings</param>
    /// <param name="sqlScriptsPath">Path to SQL scripts directory (default: "sql")</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddTenantDB(
        this IServiceCollection services,
        IConfigurationSection configurationSection,
        string sqlScriptsPath = "sql")
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));
        
        if (configurationSection == null)
            throw new ArgumentNullException(nameof(configurationSection));

        var databaseConfig = new DatabaseConfiguration();
        configurationSection.Bind(databaseConfig);

        return services.AddTenantDB(databaseConfig, sqlScriptsPath);
    }
}
