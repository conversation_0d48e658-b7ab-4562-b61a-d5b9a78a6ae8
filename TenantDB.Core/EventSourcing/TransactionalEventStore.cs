using System.Data;
using System.Text.Json;
using Dapper;
using Microsoft.Extensions.Logging;
using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing;

public class TransactionalEventStore : IEventStore
{
    private readonly IDbConnection _connection;
    private readonly IDbTransaction? _transaction;
    private readonly ILogger<TransactionalEventStore> _logger;

    public TransactionalEventStore(IDbConnection connection, IDbTransaction? transaction, ILogger<TransactionalEventStore> logger)
    {
        _connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _transaction = transaction;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<bool> SaveEventAsync(BaseEvent @event)
    {
        try
        {
            @event.EventData = @event.SerializeEventData();
            @event.Metadata = @event.SerializeMetadata();
            
            const string sql = @"
                INSERT INTO base_event (event_id, event_type, aggregate_id, aggregate_type, version, event_data, metadata, source_id, created_at)
                VALUES (@EventId, @EventType, @AggregateId, @AggregateType, @Version, @EventData::jsonb, @Metadata::jsonb, @SourceId, @CreatedAt)";

            var parameters = new
            {
                @event.EventId,
                @event.EventType,
                @event.AggregateId,
                AggregateType = @event.AggregateType.ToString().ToLowerInvariant(),
                @event.Version,
                EventData = @event.EventData.RootElement.GetRawText(),
                Metadata = @event.Metadata.RootElement.GetRawText(),
                @event.SourceId,
                @event.CreatedAt
            };

            var result = await _connection.ExecuteAsync(sql, parameters, _transaction);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving event {EventType} for aggregate {AggregateId}", @event.EventType, @event.AggregateId);
            return false;
        }
    }

    public async Task<IEnumerable<BaseEvent>> GetEventsAsync(Guid aggregateId)
    {
        try
        {
            const string sql = @"
                SELECT event_id, event_type, aggregate_id, aggregate_type, version, event_data, metadata, source_id, created_at
                FROM base_event 
                WHERE aggregate_id = @AggregateId 
                ORDER BY version ASC";

            var rows = await _connection.QueryAsync(sql, new { AggregateId = aggregateId }, _transaction);
            
            var events = new List<BaseEvent>();
            foreach (var row in rows)
            {
                var @event = CreateEventFromRow(row);
                if (@event != null)
                    events.Add(@event);
            }
            
            return events;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting events for aggregate {AggregateId}", aggregateId);
            return Enumerable.Empty<BaseEvent>();
        }
    }

    public async Task<IEnumerable<BaseEvent>> GetEventsFromVersionAsync(Guid aggregateId, int fromVersion)
    {
        try
        {
            const string sql = @"
                SELECT event_id, event_type, aggregate_id, aggregate_type, version, event_data, metadata, source_id, created_at
                FROM base_event 
                WHERE aggregate_id = @AggregateId AND version >= @FromVersion
                ORDER BY version ASC";

            var rows = await _connection.QueryAsync(sql, new { AggregateId = aggregateId, FromVersion = fromVersion }, _transaction);
            
            var events = new List<BaseEvent>();
            foreach (var row in rows)
            {
                var @event = CreateEventFromRow(row);
                if (@event != null)
                    events.Add(@event);
            }
            
            return events;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting events from version {FromVersion} for aggregate {AggregateId}", fromVersion, aggregateId);
            return Enumerable.Empty<BaseEvent>();
        }
    }

    public async Task<int> GetAggregateVersionAsync(Guid aggregateId)
    {
        try
        {
            const string sql = "SELECT COALESCE(MAX(version), 0) FROM base_event WHERE aggregate_id = @AggregateId";
            
            var version = await _connection.QuerySingleOrDefaultAsync<int>(sql, new { AggregateId = aggregateId }, _transaction);
            return version;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting version for aggregate {AggregateId}", aggregateId);
            return 0;
        }
    }

    public async Task<bool> AggregateExistsAsync(Guid aggregateId)
    {
        try
        {
            const string sql = "SELECT 1 FROM base_event WHERE aggregate_id = @AggregateId LIMIT 1";
            
            var result = await _connection.QuerySingleOrDefaultAsync<int?>(sql, new { AggregateId = aggregateId }, _transaction);
            return result.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if aggregate {AggregateId} exists", aggregateId);
            return false;
        }
    }

    public async Task<IEnumerable<EventSource>> GetEventSourcesAsync()
    {
        try
        {
            const string sql = "SELECT source_id, source_type, source_name, description, configuration, created_at FROM event_source ORDER BY source_name";
            
            var rows = await _connection.QueryAsync(sql, transaction: _transaction);
            
            var sources = new List<EventSource>();
            foreach (var row in rows)
            {
                sources.Add(new EventSource
                {
                    SourceId = row.source_id,
                    SourceType = Enum.Parse<SourceType>(row.source_type, true),
                    SourceName = row.source_name,
                    Description = row.description,
                    Configuration = JsonDocument.Parse(row.configuration?.ToString() ?? "{}"),
                    CreatedAt = row.created_at
                });
            }
            
            return sources;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting event sources");
            return Enumerable.Empty<EventSource>();
        }
    }

    public async Task<EventSource?> GetEventSourceByNameAsync(string sourceName)
    {
        try
        {
            const string sql = "SELECT source_id, source_type, source_name, description, configuration, created_at FROM event_source WHERE source_name = @SourceName";
            
            var row = await _connection.QuerySingleOrDefaultAsync(sql, new { SourceName = sourceName }, _transaction);
            
            if (row == null) return null;
            
            return new EventSource
            {
                SourceId = row.source_id,
                SourceType = Enum.Parse<SourceType>(row.source_type, true),
                SourceName = row.source_name,
                Description = row.description,
                Configuration = JsonDocument.Parse(row.configuration?.ToString() ?? "{}"),
                CreatedAt = row.created_at
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting event source {SourceName}", sourceName);
            return null;
        }
    }

    public async Task<EventSource> CreateEventSourceAsync(string sourceName, SourceType sourceType, string? description = null)
    {
        try
        {
            var eventSource = new EventSource
            {
                SourceId = Guid.NewGuid(),
                SourceName = sourceName,
                SourceType = sourceType,
                Description = description,
                Configuration = JsonDocument.Parse("{}"),
                CreatedAt = DateTime.UtcNow
            };
            
            const string sql = @"
                INSERT INTO event_source (source_id, source_type, source_name, description, configuration, created_at)
                VALUES (@SourceId, @SourceType::source_type, @SourceName, @Description, @Configuration::jsonb, @CreatedAt)";

            var parameters = new
            {
                eventSource.SourceId,
                SourceType = sourceType.ToString().ToLowerInvariant(),
                eventSource.SourceName,
                eventSource.Description,
                Configuration = eventSource.Configuration.RootElement.GetRawText(),
                eventSource.CreatedAt
            };

            await _connection.ExecuteAsync(sql, parameters, _transaction);
            return eventSource;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating event source {SourceName}", sourceName);
            throw;
        }
    }

    private BaseEvent? CreateEventFromRow(dynamic row)
    {
        try
        {
            var genericEvent = new GenericEvent
            {
                EventId = row.event_id,
                AggregateId = row.aggregate_id,
                AggregateType = Enum.Parse<EntityType>((string)row.aggregate_type, true),
                Version = row.version,
                EventData = JsonDocument.Parse(row.event_data?.ToString() ?? "{}"),
                Metadata = JsonDocument.Parse(row.metadata?.ToString() ?? "{}"),
                SourceId = row.source_id,
                CreatedAt = row.created_at,
                EventTypeName = row.event_type
            };
            
            return genericEvent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating event from database row");
            return null;
        }
    }
}