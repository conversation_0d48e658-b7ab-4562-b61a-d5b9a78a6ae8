using System.Text.Json;
using TenantDB.Core.EventSourcing.Events;
using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing.Aggregates;

public class PersonState
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? LinkedInUrl { get; set; }
    public string ConsentStatus { get; set; } = "unknown";
    public bool IsDeleted { get; set; }
}

public class PersonAggregate : Aggregate<PersonState>
{
    public override EntityType EntityType => EntityType.Person;

    public string FirstName => State.FirstName;
    public string LastName => State.LastName;
    public string? Email => State.Email;
    public string? LinkedInUrl => State.LinkedInUrl;
    public string ConsentStatus => State.ConsentStatus;
    public bool IsDeleted => State.IsDeleted;

    public string FullName => $"{FirstName} {LastName}".Trim();

    public override void Apply<TEventData>(BaseEvent<TEventData> @event)
    {
        switch (@event)
        {
            case PersonCreatedEvent personCreated:
                Apply(personCreated);
                break;
            case PersonUpdatedEvent personUpdated:
                Apply(personUpdated);
                break;
            case PersonConsentChangedEvent consentChanged:
                Apply(consentChanged);
                break;
            default:
                ApplyUntyped(@event);
                break;
        }
    }

    public void Apply(PersonCreatedEvent @event)
    {
        State.FirstName = @event.Data.FirstName;
        State.LastName = @event.Data.LastName;
        State.Email = @event.Data.Email;
        State.LinkedInUrl = @event.Data.LinkedInUrl;
        State.ConsentStatus = @event.Data.ConsentStatus;
        State.IsDeleted = false;

        Version = @event.Version;
        LastUpdated = @event.CreatedAt;
        
        if (Version == 1)
        {
            CreatedAt = @event.CreatedAt;
        }
    }

    public void Apply(PersonUpdatedEvent @event)
    {
        if (!string.IsNullOrEmpty(@event.Data.FirstName))
            State.FirstName = @event.Data.FirstName;
            
        if (!string.IsNullOrEmpty(@event.Data.LastName))
            State.LastName = @event.Data.LastName;
            
        if (@event.Data.Email != null)
            State.Email = @event.Data.Email;
            
        if (@event.Data.LinkedInUrl != null)
            State.LinkedInUrl = @event.Data.LinkedInUrl;
            
        if (!string.IsNullOrEmpty(@event.Data.ConsentStatus))
            State.ConsentStatus = @event.Data.ConsentStatus;

        Version = @event.Version;
        LastUpdated = @event.CreatedAt;
    }

    public void Apply(PersonConsentChangedEvent @event)
    {
        State.ConsentStatus = @event.Data.ConsentStatus;

        Version = @event.Version;
        LastUpdated = @event.CreatedAt;
    }

    public static PersonCreatedEvent CreatePerson(Guid? personId, string firstName, string lastName, string? email = null, string? linkedInUrl = null)
    {
        var id = personId ?? Guid.NewGuid();
        return new PersonCreatedEvent(id, firstName, lastName, email, linkedInUrl)
        {
            Version = 1
        };
    }

    public PersonUpdatedEvent UpdatePerson(PersonUpdatedEventData updateData)
    {
        return new PersonUpdatedEvent(Id, updateData)
        {
            Version = Version + 1
        };
    }

    public PersonConsentChangedEvent ChangeConsent(string consentStatus, string method, string? reason = null)
    {
        return new PersonConsentChangedEvent(Id, consentStatus, method, reason)
        {
            Version = Version + 1
        };
    }

    public bool CanContact()
    {
        return !IsDeleted && ConsentStatus == "opted_in";
    }

    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(FirstName) && !string.IsNullOrWhiteSpace(LastName);
    }
}
