using System.Text.Json;

namespace TenantDB.Core.EventSourcing.Models;

public abstract class Aggregate
{
    public Guid Id { get; set; }
    public abstract EntityType EntityType { get; }
    public JsonDocument Data { get; set; } = JsonDocument.Parse("{}");
    public JsonDocument Metadata { get; set; } = JsonDocument.Parse("{}");
    public int Version { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public abstract void Apply(BaseEvent @event);

    public abstract JsonDocument GetState();

    public abstract void SetState(JsonDocument state);
}

public abstract class Aggregate<TState> : Aggregate where TState : class, new()
{
    public TState State { get; set; } = new();

    public override JsonDocument GetState()
    {
        var json = JsonSerializer.Serialize(State);
        return JsonDocument.Parse(json);
    }

    public override void SetState(JsonDocument state)
    {
        var json = state.RootElement.GetRawText();
        State = JsonSerializer.Deserialize<TState>(json) ?? new TState();
    }

    public abstract void Apply<TEventData>(BaseEvent<TEventData> @event) where TEventData : class, new();

    public override void Apply(BaseEvent @event)
    {
        var eventType = @event.GetType();
        var applyMethods = GetType().GetMethods()
            .Where(m => m.Name == nameof(Apply) && 
                       m.GetParameters().Length == 1 && 
                       m.GetParameters()[0].ParameterType == eventType &&
                       !m.IsGenericMethodDefinition)
            .ToList();
        
        var applyMethod = applyMethods.FirstOrDefault();
        if (applyMethod != null)
        {
            applyMethod.Invoke(this, [@event]);
            return;
        }

        var baseEventType = eventType.BaseType;
        if (baseEventType != null && baseEventType.IsGenericType)
        {
            var genericDef = baseEventType.GetGenericTypeDefinition();
            if (genericDef == typeof(BaseEvent<>))
            {
                var methods = GetType().GetMethods()
                    .Where(m => m.Name == nameof(Apply) && m.IsGenericMethodDefinition)
                    .ToList();
                var genericApply = methods.FirstOrDefault(m => m.GetParameters().Length == 1);
                if (genericApply != null)
                {
                    var typeArgs = baseEventType.GetGenericArguments();
                    var concreteApply = genericApply.MakeGenericMethod(typeArgs[0]);
                    concreteApply.Invoke(this, [@event]);
                    return;
                }
            }
        }

        ApplyUntyped(@event);
    }

    protected virtual void ApplyUntyped(BaseEvent @event)
    {
        Version = @event.Version;
        LastUpdated = @event.CreatedAt;
    }
}

public enum EntityType
{
    Person,
    Company,
    Website,
    Place
}
