using System.Data;

namespace TenantDB.Core.DataLayer;

/// <summary>
/// Simplified data layer interface for Dapper-based database operations
/// </summary>
public interface IDataLayer
{
    /// <summary>
    /// Gets a database connection for the specified database
    /// </summary>
    /// <param name="databaseName">Optional specific database name. If null, uses default database</param>
    /// <returns>An open database connection</returns>
    Task<IDbConnection> GetConnectionAsync(string? databaseName = null);

    /// <summary>
    /// Checks if a database exists
    /// </summary>
    /// <param name="databaseName">The name of the database to check</param>
    /// <returns>True if the database exists, false otherwise</returns>
    Task<bool> DatabaseExistsAsync(string databaseName);

    /// <summary>
    /// Creates a new database
    /// </summary>
    /// <param name="databaseName">The name of the database to create</param>
    /// <returns>True if the database was created successfully</returns>
    Task<bool> CreateDatabaseAsync(string databaseName);

    /// <summary>
    /// Drops a database
    /// </summary>
    /// <param name="databaseName">The name of the database to drop</param>
    /// <returns>True if the database was dropped successfully</returns>
    Task<bool> DropDatabaseAsync(string databaseName);

    /// <summary>
    /// Creates a database session with transaction support
    /// </summary>
    /// <param name="databaseName">Optional specific database name. If null, uses default database</param>
    /// <returns>A database session with commit/rollback capabilities</returns>
    Task<IDatabaseSession> CreateSessionAsync(string? databaseName = null);
}
