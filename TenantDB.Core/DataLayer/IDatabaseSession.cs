using System.Data;
using TenantDB.Core.EventSourcing;

namespace TenantDB.Core.DataLayer;

/// <summary>
/// Database session interface that provides transaction support with commit/rollback functionality
/// </summary>
public interface IDatabaseSession : IDisposable
{
    /// <summary>
    /// Gets the underlying database connection
    /// </summary>
    IDbConnection Connection { get; }

    /// <summary>
    /// Gets the current database transaction, if any
    /// </summary>
    IDbTransaction? Transaction { get; }

    /// <summary>
    /// Gets a value indicating whether a transaction is currently active
    /// </summary>
    bool HasActiveTransaction { get; }

    /// <summary>
    /// Begins a new database transaction
    /// </summary>
    /// <returns>The database transaction</returns>
    IDbTransaction BeginTransaction();

    /// <summary>
    /// Commits the current transaction
    /// </summary>
    /// <returns>Task representing the asynchronous operation</returns>
    Task CommitAsync();

    /// <summary>
    /// Rolls back the current transaction
    /// </summary>
    /// <returns>Task representing the asynchronous operation</returns>
    Task RollbackAsync();

    /// <summary>
    /// Gets the event store for this session
    /// </summary>
    IEventStore EventStore { get; }

    /// <summary>
    /// Gets the aggregate repository for this session
    /// </summary>
    IAggregateRepository AggregateRepository { get; }
}