using System.Data;
using Microsoft.Extensions.Logging;
using TenantDB.Core.EventSourcing;

namespace TenantDB.Core.DataLayer;

public class DatabaseSession : IDatabaseSession
{
    private readonly ILogger<DatabaseSession> _logger;
    private readonly Func<IDbConnection, IDbTransaction?, IEventStore> _eventStoreFactory;
    private readonly Func<IDbConnection, IDbTransaction?, IAggregateRepository> _aggregateRepositoryFactory;
    private bool _disposed;

    public DatabaseSession(
        IDbConnection connection,
        ILogger<DatabaseSession> logger,
        Func<IDbConnection, IDbTransaction?, IEventStore> eventStoreFactory,
        Func<IDbConnection, IDbTransaction?, IAggregateRepository> aggregateRepositoryFactory)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _eventStoreFactory = eventStoreFactory ?? throw new ArgumentNullException(nameof(eventStoreFactory));
        _aggregateRepositoryFactory = aggregateRepositoryFactory ?? throw new ArgumentNullException(nameof(aggregateRepositoryFactory));
    }

    public IDbConnection Connection { get; }

    public IDbTransaction? Transaction { get; private set; }

    public bool HasActiveTransaction => Transaction != null;

    private IEventStore? _eventStore;
    public IEventStore EventStore => _eventStore ??= _eventStoreFactory(Connection, Transaction);

    private IAggregateRepository? _aggregateRepository;
    public IAggregateRepository AggregateRepository => _aggregateRepository ??= _aggregateRepositoryFactory(Connection, Transaction);

    public IDbTransaction BeginTransaction()
    {
        if (HasActiveTransaction)
        {
            throw new InvalidOperationException("A transaction is already active. Commit or rollback the current transaction before beginning a new one.");
        }

        Transaction = Connection.BeginTransaction();
        _logger.LogDebug("Database transaction begun");

        // Recreate services with new transaction
        _eventStore = null;
        _aggregateRepository = null;

        return Transaction;
    }

    public async Task CommitAsync()
    {
        if (!HasActiveTransaction)
        {
            throw new InvalidOperationException("No active transaction to commit.");
        }

        try
        {
            Transaction!.Commit();
            _logger.LogDebug("Database transaction committed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to commit database transaction");
            throw;
        }
        finally
        {
            Transaction?.Dispose();
            Transaction = null;
            
            _eventStore = null;
            _aggregateRepository = null;
        }

        await Task.CompletedTask;
    }

    public async Task RollbackAsync()
    {
        if (!HasActiveTransaction)
        {
            throw new InvalidOperationException("No active transaction to rollback.");
        }

        try
        {
            Transaction!.Rollback();
            _logger.LogDebug("Database transaction rolled back");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to rollback database transaction");
            throw;
        }
        finally
        {
            Transaction?.Dispose();
            Transaction = null;
            
            _eventStore = null;
            _aggregateRepository = null;
        }

        await Task.CompletedTask;
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            if (HasActiveTransaction)
            {
                _logger.LogWarning("Disposing DatabaseSession with active transaction. Rolling back transaction.");
                Transaction!.Rollback();
                Transaction.Dispose();
            }

            Connection?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing DatabaseSession");
        }
        finally
        {
            _disposed = true;
        }
    }
}