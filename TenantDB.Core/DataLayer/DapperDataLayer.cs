using System.Data;
using Dapper;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Connection;
using TenantDB.Core.EventSourcing;

namespace TenantDB.Core.DataLayer;

/// <summary>
/// Dapper-based implementation of the data layer
/// </summary>
public class DapperDataLayer : IDataLayer
{
    private readonly IConnectionFactory _connectionFactory;
    private readonly ILogger<DapperDataLayer> _logger;
    private readonly ILoggerFactory _loggerFactory;

    public DapperDataLayer(IConnectionFactory connectionFactory, ILogger<DapperDataLayer> logger, ILoggerFactory loggerFactory)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
    }

    /// <inheritdoc />
    public async Task<IDbConnection> GetConnectionAsync(string? databaseName = null)
    {
        var connection = databaseName == null
            ? await _connectionFactory.CreateConnectionAsync()
            : await _connectionFactory.CreateConnectionAsync(databaseName);
        return connection;
    }

    /// <inheritdoc />
    public async Task<bool> DatabaseExistsAsync(string databaseName)
    {
        try
        {
            using var connection = await _connectionFactory.CreateSystemConnectionAsync();
            const string sql = "SELECT 1 FROM pg_database WHERE datname = @databaseName";
            var result = await connection.QuerySingleOrDefaultAsync<int?>(sql, new { databaseName });
            return result.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if database {DatabaseName} exists", databaseName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> CreateDatabaseAsync(string databaseName)
    {
        try
        {
            using var connection = await _connectionFactory.CreateSystemConnectionAsync();
            var sql = $"CREATE DATABASE \"{databaseName}\"";
            await connection.ExecuteAsync(sql);
            _logger.LogInformation("Database {DatabaseName} created successfully", databaseName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating database {DatabaseName}", databaseName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DropDatabaseAsync(string databaseName)
    {
        try
        {
            using var connection = await _connectionFactory.CreateSystemConnectionAsync();
            var sql = $"DROP DATABASE IF EXISTS \"{databaseName}\"";
            await connection.ExecuteAsync(sql);
            _logger.LogInformation("Database {DatabaseName} dropped successfully", databaseName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dropping database {DatabaseName}", databaseName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<IDatabaseSession> CreateSessionAsync(string? databaseName = null)
    {
        var connection = databaseName == null
            ? await _connectionFactory.CreateConnectionAsync()
            : await _connectionFactory.CreateConnectionAsync(databaseName);

        var eventStoreFactory = (IDbConnection conn, IDbTransaction? trans) => 
            new TransactionalEventStore(conn, trans, _loggerFactory.CreateLogger<TransactionalEventStore>());
        
        var aggregateRepositoryFactory = (IDbConnection conn, IDbTransaction? trans) =>
        {
            var eventStore = eventStoreFactory(conn, trans);
            return new TransactionalAggregateRepository(conn, trans, eventStore, _loggerFactory.CreateLogger<TransactionalAggregateRepository>());
        };

        return new DatabaseSession(
            connection,
            _loggerFactory.CreateLogger<DatabaseSession>(),
            eventStoreFactory,
            aggregateRepositoryFactory);
    }
}
