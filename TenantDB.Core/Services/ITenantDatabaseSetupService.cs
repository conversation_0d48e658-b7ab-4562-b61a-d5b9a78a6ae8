namespace TenantDB.Core.Services;

/// <summary>
/// Service interface for setting up databases
/// </summary>
public interface ITenantDatabaseSetupService
{
    /// <summary>
    /// Sets up a complete database schema
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the setup was successful, false otherwise</returns>
    Task<bool> SetupDatabaseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the database exists and has the required schema
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the database exists and is properly set up, false otherwise</returns>
    Task<bool> DatabaseExistsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes the database schema (use with caution)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the database was removed successfully, false otherwise</returns>
    Task<bool> RemoveDatabaseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the configured database name
    /// </summary>
    /// <returns>The database name</returns>
    string GetDatabaseName();
}
