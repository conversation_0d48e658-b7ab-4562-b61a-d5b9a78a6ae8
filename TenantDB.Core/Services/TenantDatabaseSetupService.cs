using Dapper;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Configuration;
using TenantDB.Core.DataLayer;

namespace TenantDB.Core.Services;

/// <summary>
/// Service for setting up databases with the complete schema
/// </summary>
public class TenantDatabaseSetupService : ITenantDatabaseSetupService
{
    private readonly IDataLayer _dataLayer;
    private readonly ILogger<TenantDatabaseSetupService> _logger;
    private readonly DatabaseConfiguration _configuration;
    private readonly string _sqlScriptsPath;

    public TenantDatabaseSetupService(
        IDataLayer dataLayer,
        ILogger<TenantDatabaseSetupService> logger,
        DatabaseConfiguration configuration,
        string sqlScriptsPath = "sql")
    {
        _dataLayer = dataLayer ?? throw new ArgumentNullException(nameof(dataLayer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _sqlScriptsPath = sqlScriptsPath;
    }

    /// <inheritdoc />
    public async Task<bool> SetupDatabaseAsync(CancellationToken cancellationToken = default)
    {
        var databaseName = GetDatabaseName();

        try
        {
            _logger.LogInformation("Starting database setup (database: {DatabaseName})", databaseName);

            // Check if database already exists
            if (await DatabaseExistsAsync(cancellationToken))
            {
                _logger.LogWarning("Database {DatabaseName} already exists", databaseName);
                return false;
            }

            // Create the database
            var result = await _dataLayer.CreateDatabaseAsync(databaseName);

            _logger.LogInformation("Database {DatabaseName} created successfully", databaseName);

            // Execute SQL scripts to set up the schema
            await ExecuteSqlScriptsAsync(databaseName, cancellationToken);

            _logger.LogInformation("Database setup completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to setup database");

            // Attempt cleanup if database was created but schema setup failed
            try
            {
                if (await DatabaseExistsAsync(cancellationToken))
                {
                    await RemoveDatabaseAsync(cancellationToken);
                }
            }
            catch (Exception cleanupEx)
            {
                _logger.LogError(cleanupEx, "Failed to cleanup database after setup failure");
            }

            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DatabaseExistsAsync(CancellationToken cancellationToken = default)
    {
        var databaseName = GetDatabaseName();

        try
        {
            return await _dataLayer.DatabaseExistsAsync(databaseName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if database exists");
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> RemoveDatabaseAsync(CancellationToken cancellationToken = default)
    {
        var databaseName = GetDatabaseName();

        try
        {
            _logger.LogWarning("Removing database {DatabaseName}", databaseName);
            var result = await _dataLayer.DropDatabaseAsync(databaseName);

            if (result)
            {
                _logger.LogInformation("Database {DatabaseName} removed successfully", databaseName);
            }
            else
            {
                _logger.LogError("Failed to remove database {DatabaseName}", databaseName);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove database");
            return false;
        }
    }

    /// <inheritdoc />
    public string GetDatabaseName()
    {
        return _configuration.Database;
    }

    private async Task ExecuteSqlScriptsAsync(string databaseName, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing SQL scripts for database {DatabaseName}", databaseName);

        // Get all SQL files in order
        var sqlFiles = GetSqlFilesInOrder();
        
        foreach (var sqlFile in sqlFiles)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            _logger.LogDebug("Executing SQL script: {SqlFile}", sqlFile);
            
            var sqlContent = await File.ReadAllTextAsync(sqlFile, cancellationToken);

            // Execute the entire SQL file content at once
            try
            {
                using var connection = await _dataLayer.GetConnectionAsync(databaseName);
                await connection.ExecuteAsync(sqlContent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to execute SQL script {SqlFile}", sqlFile);
                throw;
            }
            
            _logger.LogDebug("Successfully executed SQL script: {SqlFile}", sqlFile);
        }
        
        _logger.LogInformation("All SQL scripts executed successfully for database {DatabaseName}", databaseName);
    }

    private string[] GetSqlFilesInOrder()
    {
        if (!Directory.Exists(_sqlScriptsPath))
        {
            throw new DirectoryNotFoundException($"SQL scripts directory not found: {_sqlScriptsPath}");
        }

        var sqlFiles = Directory.GetFiles(_sqlScriptsPath, "*.sql")
            .Where(f => !Path.GetFileName(f).Equals("init.sql", StringComparison.OrdinalIgnoreCase))
            .OrderBy(f => Path.GetFileName(f))
            .ToArray();

        if (sqlFiles.Length == 0)
        {
            throw new InvalidOperationException($"No SQL files found in directory: {_sqlScriptsPath}");
        }

        _logger.LogDebug("Found {Count} SQL files to execute: {Files}", sqlFiles.Length, string.Join(", ", sqlFiles.Select(Path.GetFileName)));
        
        return sqlFiles;
    }


}
