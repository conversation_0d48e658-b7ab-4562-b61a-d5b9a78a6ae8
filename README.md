# TenantDB Framework

A minimal .NET 9 framework for tenant-based PostgreSQL databases with event sourcing capabilities using Dapper.

## Overview

TenantDB is a lightweight framework designed for multi-tenant applications with event sourcing. It provides a simple data layer using Dapper, automatic tenant database management, and a complete event sourcing implementation with JSONB storage.

## Features

- **🏢 Tenant Database Management**: Automatically create and manage separate databases for each tenant
- **⚡ Event Sourcing**: Complete event sourcing implementation with aggregates and event store
- **🚀 Dapper Integration**: Fast, lightweight ORM for database operations
- **📦 JSONB Support**: Efficient JSON storage for event data and aggregate state
- **🛠️ CLI Setup Tool**: Command-line tool for easy database setup
- **🧪 Comprehensive Testing**: Unit tests with high coverage using xUnit and FluentAssertions
- **📋 Dependency Injection**: Full support for .NET dependency injection

## Architecture

The framework follows these design patterns:
- **Event Sourcing**: Events as the source of truth with aggregate rebuilding
- **Data Layer Pattern**: Simple abstraction using Dapper (`IDataLayer`, `DapperDataLayer`)
- **Event Store**: Persistent event storage (`IEventStore`, `DapperEventStore`)
- **Aggregate Repository**: Aggregate persistence and loading (`IAggregateRepository`)
- **Service Layer**: High-level tenant management operations

## Event Sourcing Schema

The framework includes a complete event sourcing schema:
- **Event Sources**: Track sources of events (users, services, scrapers, etc.)
- **Base Events**: Core event store with aggregate versioning and JSONB data
- **Aggregates**: Current state of entities (Person, Company, Website, Place)
- **Aggregate Entities**: Basic entity tracking
- **Views**: Convenient views for each aggregate type

## Quick Start

### 1. Set up PostgreSQL with Docker

```bash
# Start PostgreSQL instance
docker-compose up -d
```

### 2. Use the CLI Setup Tool

```bash
# Build the setup tool
dotnet build TenantDB.Setup

# Run with interactive prompts
dotnet run --project TenantDB.Setup

# Or with command line arguments
dotnet run --project TenantDB.Setup -- --host localhost --username postgres --password secret

# Or with environment variables
TENANTDB_USERNAME=postgres TENANTDB_PASSWORD=secret dotnet run --project TenantDB.Setup
```

### 3. Configure Your Application

```csharp
using Microsoft.Extensions.DependencyInjection;
using TenantDB.Core.Configuration;
using TenantDB.Core.Extensions;

var services = new ServiceCollection();

// Configure database
var databaseConfig = new DatabaseConfiguration
{
    Host = "localhost",
    Port = 5432,
    Database = "postgres",
    Username = "postgres",
    Password = "your_password"
};

// Register TenantDB services (includes event sourcing)
services.AddTenantDB(databaseConfig, "sql");
```

### 4. Use Event Sourcing

```csharp
using TenantDB.Core.EventSourcing;
using TenantDB.Core.EventSourcing.Aggregates;
using TenantDB.Core.EventSourcing.Events;

// Get services
var eventStore = serviceProvider.GetRequiredService<IEventStore>();
var aggregateRepo = serviceProvider.GetRequiredService<IAggregateRepository>();

// Create an event source
var eventSource = await eventStore.CreateEventSourceAsync("api", SourceType.Api);

// Create a person
var personId = Guid.NewGuid();
var createEvent = PersonAggregate.CreatePerson(personId, "John", "Doe", "<EMAIL>");
createEvent.SourceId = eventSource.SourceId;

// Save the event
await eventStore.SaveEventAsync(createEvent);

// Create and save the aggregate
var person = new PersonAggregate { Id = personId };
person.Apply(createEvent);
await aggregateRepo.SaveAsync(person);
```

### 5. Use Dapper Directly

```csharp
using Dapper;
using TenantDB.Core.DataLayer;

// Get a connection to the database
var dataLayer = serviceProvider.GetRequiredService<IDataLayer>();
using var connection = await dataLayer.GetConnectionAsync();

// Use Dapper to query the database
var events = await connection.QueryAsync("SELECT * FROM base_event WHERE aggregate_id = @id", new { id = personId });
```

## Project Structure

```
TenantDB/
├── TenantDB.Core/              # Core framework library
│   ├── Configuration/          # Database configuration
│   ├── Connection/             # Connection factory
│   ├── DataLayer/              # Dapper-based data access layer
│   ├── EventSourcing/          # Event sourcing implementation
│   │   ├── Models/             # Base events, aggregates, event sources
│   │   ├── Events/             # Specific event implementations
│   │   ├── Aggregates/         # Specific aggregate implementations
│   │   ├── IEventStore.cs      # Event store interface
│   │   ├── DapperEventStore.cs # Dapper-based event store
│   │   └── ...                 # Other event sourcing components
│   ├── Services/               # High-level tenant management services
│   └── Extensions/             # Dependency injection extensions
├── TenantDB.Setup/             # CLI setup tool
├── TenantDB.Tests/             # Unit tests
│   ├── EventSourcing/          # Event sourcing tests
│   ├── Services/               # Service tests
│   └── Configuration/          # Configuration tests
├── Example/                    # Example console application
├── sql/                        # Database schema scripts
├── docker-compose.yml          # PostgreSQL setup
├── EXTENDING_EVENTS.md         # Guide for extending event sourcing
└── .env                        # Environment configuration
```

## Environment Configuration

The `.env` file contains database configuration:

```env
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=tenantdb
POSTGRES_USER=tenantdb_user
POSTGRES_PASSWORD=tenantdb_password
```

## CLI Setup Tool

The framework includes a command-line setup tool for easy database initialization:

```bash
# Show help
dotnet run --project TenantDB.Setup -- --help

# Interactive setup
dotnet run --project TenantDB.Setup

# With parameters
dotnet run --project TenantDB.Setup -- \
  --host ************ \
  --port 5432 \
  --username tenantdb_user \
  --password tenantdb_password

# With environment variables
export TENANTDB_USERNAME=postgres
export TENANTDB_PASSWORD=secret
dotnet run --project TenantDB.Setup
```

## Event Sourcing

The framework provides a complete event sourcing implementation:

- **Events**: Immutable records of what happened (e.g., `PersonCreated`, `PersonUpdated`)
- **Aggregates**: Current state built from events (e.g., `PersonAggregate`)
- **Event Store**: Persistent storage for events with JSONB data
- **Aggregate Repository**: Loading and saving aggregates

See `EXTENDING_EVENTS.md` for detailed guidance on creating new events and aggregates.

## Running Tests

```bash
# Run all tests
dotnet test

# Run specific test categories
dotnet test --filter "FullyQualifiedName~EventSourcing"
dotnet test --filter "FullyQualifiedName~PersonAggregate"
```

## Running the Example

```bash
cd Example
dotnet run
```

## Dependencies

- .NET 9.0
- Dapper (lightweight ORM)
- Npgsql (PostgreSQL driver)
- Microsoft.Extensions.* (Configuration, DependencyInjection, Logging, Hosting)
- xUnit, FluentAssertions, Moq (for testing)

## Key Concepts

### Event Sourcing
Events are the source of truth. Aggregates are built by replaying events in order. This provides:
- Complete audit trail
- Ability to rebuild state at any point in time
- Temporal queries
- Event-driven architecture support

### Multi-Tenancy
Each tenant gets their own PostgreSQL database with the complete schema. This provides:
- Data isolation
- Independent scaling
- Tenant-specific customizations
- Simplified backup and restore

### JSONB Storage
Event data and aggregate state are stored as JSONB, providing:
- Flexible schema evolution
- Efficient querying with PostgreSQL's JSONB operators
- No need for complex object-relational mapping

## License

This project is intended as an internal SDK for event-based database management.

## PersonAggregate & Person is awkward. Our base class needs to be named differently