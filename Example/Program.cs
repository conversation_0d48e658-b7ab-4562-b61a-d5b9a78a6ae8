using Dapper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Configuration;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing;
using TenantDB.Core.EventSourcing.Aggregates;
using TenantDB.Core.EventSourcing.Events;
using TenantDB.Core.EventSourcing.Models;
using TenantDB.Core.Extensions;
using TenantDB.Core.Services;

namespace TenantDB.Example;

class Program
{
    static async Task Main(string[] args)
    {
        var services = new ServiceCollection();
        
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        var databaseConfig = new DatabaseConfiguration
        {
            Host = "localhost",
            Port = 5433,
            Database = "tenantdb_example",
            Username = "tenantdb_user",
            Password = "tenantdb_password"
        };
        
        services.AddTenantDB(databaseConfig, "sql");
        
        var serviceProvider = services.BuildServiceProvider();
        
        var tenantSetupService = serviceProvider.GetRequiredService<ITenantDatabaseSetupService>();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        
        try
        {
            logger.LogInformation("TenantDB Framework Example");
            logger.LogInformation("========================");

            logger.LogInformation("Checking if database exists...");
            var exists = await tenantSetupService.DatabaseExistsAsync();

            if (exists)
            {
                logger.LogInformation("Database already exists. Removing it first...");
                var removed = await tenantSetupService.RemoveDatabaseAsync();
                if (!removed)
                {
                    // Database may be in use - try setup anyway
                    logger.LogWarning("Failed to remove existing database (probably a different open connection - pgadmin). Continuing anyway...");
                }
            }

            logger.LogInformation("Setting up database...");
            var success = await tenantSetupService.SetupDatabaseAsync();

            if (success)
            {
                logger.LogInformation("Database setup completed successfully!");
                logger.LogInformation("Database name: {DatabaseName}", tenantSetupService.GetDatabaseName());

                var verifyExists = await tenantSetupService.DatabaseExistsAsync();
                logger.LogInformation("Database exists verification: {Exists}", verifyExists);

                logger.LogInformation("Demonstrating transaction functionality...");
                var dataLayer = serviceProvider.GetRequiredService<IDataLayer>();
                var databaseName = tenantSetupService.GetDatabaseName();

                await DemonstrateTransactionWorkflow(dataLayer, databaseName, logger);
            }
            else
            {
                logger.LogError("Failed to set up database");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while running the example");
        }
        finally
        {
            await serviceProvider.DisposeAsync();
        }
        
        logger.LogInformation("Example completed.");
    }

    private static async Task DemonstrateTransactionWorkflow(IDataLayer dataLayer, string databaseName, ILogger logger)
    {
        try
        {
            // This example is very basic, the idea is that we just found a new person through some arbitrary API and are uploading it.
            using var session = await dataLayer.CreateSessionAsync(databaseName);
            
            logger.LogInformation("Creating database session and beginning transaction...");
            session.BeginTransaction();


            // Get the appropiate event source or create it
            var eventSource = await session.EventStore.GetEventSourceByNameAsync("example_api") 
                ?? await session.EventStore.CreateEventSourceAsync("example_api", SourceType.Api, "Example application API");

            logger.LogInformation("Using event source: {SourceName} ({SourceId})", eventSource.SourceName, eventSource.SourceId);

            // Create a new person with some basic data and assign the source
            var personId = Guid.NewGuid();
            logger.LogInformation("Creating person with ID: {PersonId}", personId);

            var createEvent = PersonAggregate.CreatePerson(personId, "John", "Doe", "<EMAIL>");
            createEvent.SourceId = eventSource.SourceId;

            // Save the create person event (transaction is started here)
            var eventSaved = await session.EventStore.SaveEventAsync(createEvent);
            if (!eventSaved)
            {
                throw new Exception("Failed to save person creation event");
            }

            // Create the aggregate - executes the query in the same transaction
            var person = new PersonAggregate { Id = personId };
            person.Apply(createEvent);
            var aggregateSaved = await session.AggregateRepository.SaveAsync(person);
            if (!aggregateSaved)
            {
                throw new Exception("Failed to save person aggregate");
            }

            logger.LogInformation("Person created: {FullName} ({Email})", person.FullName, person.Email);

            // Updating the person, still in the same tx
            logger.LogInformation("Updating person's last name and email...");
            
            var updateData = new PersonUpdatedEventData
            {
                FirstName = "John",
                LastName = "Smith",
                Email = "<EMAIL>",
                LinkedInUrl = person.LinkedInUrl
            };
            
            var updateEvent = person.UpdatePerson(updateData);
            updateEvent.SourceId = eventSource.SourceId;

            // Add the new update event to the tx
            var updateEventSaved = await session.EventStore.SaveEventAsync(updateEvent);
            if (!updateEventSaved)
            {
                throw new Exception("Failed to save person update event");
            }

            // Update the aggregate
            person.Apply(updateEvent);
            var updatedAggregateSaved = await session.AggregateRepository.SaveAsync(person);
            if (!updatedAggregateSaved)
            {
                throw new Exception("Failed to save updated person aggregate");
            }

            logger.LogInformation("Person updated: {FullName} ({Email})", person.FullName, person.Email);

            logger.LogInformation("Updating person's consent status...");

            // Add some consent data
            var consentEvent = person.ChangeConsent("opted_in", "api", "User explicitly opted in via example application");
            consentEvent.SourceId = eventSource.SourceId;

            var consentEventSaved = await session.EventStore.SaveEventAsync(consentEvent);
            if (!consentEventSaved)
            {
                throw new Exception("Failed to save person consent change event");
            }

            // We update the aggregate again (You can also do this once at the end of the TX - this however
            // keeps the aggregate in memory in a valid state)
            person.Apply(consentEvent);
            var consentAggregateSaved = await session.AggregateRepository.SaveAsync(person);
            if (!consentAggregateSaved)
            {
                throw new Exception("Failed to save person aggregate with consent update");
            }

            logger.LogInformation("Person consent updated: {FullName} - Consent: {ConsentStatus}", person.FullName, person.ConsentStatus);

            logger.LogInformation("Committing transaction...");

            // Commit the transaction
            await session.CommitAsync();

            logger.LogInformation("Transaction committed successfully!");

            logger.LogInformation("Final person state:");
            logger.LogInformation("  ID: {Id}", person.Id);
            logger.LogInformation("  Full Name: {FullName}", person.FullName);
            logger.LogInformation("  Email: {Email}", person.Email);
            logger.LogInformation("  Consent Status: {ConsentStatus}", person.ConsentStatus);
            logger.LogInformation("  Version: {Version}", person.Version);
            logger.LogInformation("  Created: {CreatedAt}", person.CreatedAt);
            logger.LogInformation("  Last Updated: {LastUpdated}", person.LastUpdated);

            logger.LogInformation("Demonstrating event sourcing reconstruction...");
            using var verificationSession = await dataLayer.CreateSessionAsync(databaseName);
            var reconstructedPerson = await verificationSession.AggregateRepository.LoadFromEventsAsync<PersonAggregate>(personId);
            
            if (reconstructedPerson != null)
            {
                logger.LogInformation("Successfully reconstructed person from events:");
                logger.LogInformation("  Reconstructed Full Name: {FullName}", reconstructedPerson.FullName);
                logger.LogInformation("  Reconstructed Email: {Email}", reconstructedPerson.Email);
                logger.LogInformation("  Reconstructed Consent: {ConsentStatus}", reconstructedPerson.ConsentStatus);
                logger.LogInformation("  Reconstructed Version: {Version}", reconstructedPerson.Version);
            }
            else
            {
                logger.LogWarning("Failed to reconstruct person from events");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during transaction workflow demonstration");
            
            try
            {
                using var rollbackSession = await dataLayer.CreateSessionAsync(databaseName);
                if (rollbackSession.HasActiveTransaction)
                {
                    await rollbackSession.RollbackAsync();
                    logger.LogInformation("Transaction rolled back due to error");
                }
            }
            catch (Exception rollbackEx)
            {
                logger.LogError(rollbackEx, "Failed to rollback transaction");
            }
        }
    }
}
